
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import datetime
import sys

def send_email(to_email, option1=None, option2=None, option3=None):
    # Email credentials and server setup
    from_email = "<EMAIL>"
    password = "Ovxav*2o"
    smtp_server = "smtp.zoho.com"
    smtp_port = 465

    # Get current time
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %I:%M:%S %p")

    # If options are not provided as parameters, prompt user for each option
    if option1 is None or option2 is None or option3 is None:
        def get_option(prompt):
            while True:
                val = input(prompt).strip().lower().replace(' ', '')
                if val in ("1", "yes", "y"):
                    return True
                if val in ("0", "no", "n"):
                    return False
                print("Please enter 1/yes/y for yes or 0/no/n for no.")

        if option1 is None:
            option1 = get_option("Check option 1 (°)? (1 for yes, 0 for no): ")
        if option2 is None:
            option2 = get_option("Check option 2 (✦)? (1 for yes, 0 for no): ")
        if option3 is None:
            option3 = get_option("Check option 3 (✦)? (1 for yes, 0 for no): ")

    # Create HTML body with checkboxes, mark checked if selected
    html = f"""
    <html>
      <body>
        <p>The current time is: <b>{current_time}</b></p>
        <form>
          <input type="checkbox" name="option1"{' checked' if option1 else ''}> °<br>
          <input type="checkbox" name="option2"{' checked' if option2 else ''}> ✦<br>
          <input type="checkbox" name="option3"{' checked' if option3 else ''}> ✦<br>
        </form>
      </body>
    </html>
    """

    # Create email message
    msg = MIMEMultipart("alternative")
    msg["Subject"] = "Current Time with Options"
    msg["From"] = from_email
    msg["To"] = to_email
    msg.attach(MIMEText(html, "html"))

    # Send email with error handling
    try:
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(from_email, password)
            server.sendmail(from_email, to_email, msg.as_string())
        print("Email sent successfully.")
        print(f"Email sent at: {datetime.datetime.now().strftime('%Y-%m-%d %I:%M:%S %p')}")
        
    except Exception as e:
        print(f"Failed to send email: {e}")

# Usage
if __name__ == "__main__":
    # Check if command line arguments are provided
    if len(sys.argv) == 4:
        # Parse command line arguments: python eMail.py option1 option2 option3
        try:
            option1 = bool(int(sys.argv[1]))
            option2 = bool(int(sys.argv[2]))
            option3 = bool(int(sys.argv[3]))
            print(f"Running with options: {option1}, {option2}, {option3}")
            send_email("<EMAIL>", option1, option2, option3)
        except (ValueError, IndexError) as e:
            print(f"Error parsing arguments: {e}")
            print("Usage: python eMail.py <option1> <option2> <option3>")
            print("Where each option is 1 (yes) or 0 (no)")
            print("Example: python eMail.py 1 0 0")
    elif len(sys.argv) > 1:
        print(f"Expected 3 arguments, got {len(sys.argv)-1}")
        print("Usage: python eMail.py <option1> <option2> <option3>")
        print("Where each option is 1 (yes) or 0 (no)")
        print("Example: python eMail.py 1 0 0")
    else:
        # No command line arguments, use interactive mode
        send_email("<EMAIL>")
